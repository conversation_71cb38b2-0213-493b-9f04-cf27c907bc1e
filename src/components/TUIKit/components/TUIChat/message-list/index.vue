<template>
  <div class="tui-chat" :class="[isH5 ? 'tui-chat-h5' : '']">
    <!-- <JoinGroupCard v-if="isH5" /> -->
    <div id="tui-chat-main" class="tui-chat-main" @click="closeChatPop">
      <!-- Safe Tips -->
      <div v-if="isOfficial" class="tui-chat-safe-tips">
        <!-- <span>
          {{ TUITranslateService.t('TUIChat.【安全提示】本 APP 仅用于体验腾讯云即时通信 IM
          产品功能，不可用于业务洽谈与拓展。请勿轻信汇款、中奖等涉及钱款的信息，勿轻易拨打陌生电话，谨防上当受骗。') }}
        </span> -->
        <a @click="openComplaintLink(Link.complaint)">
          {{ TUITranslateService.t('TUIChat.点此投诉') }}
        </a>
      </div>
      <MessageGroupApplication v-if="isGroup" :key="props.groupID" :groupID="props.groupID" />
      <!-- Message List -->

      <ul id="messageScrollList" ref="messageListRef"
        :class="['tui-message-list', chatStore.isTyping ? 'tui-message-list-hidden' : '']"
        @click="onMessageListBackgroundClick" @dblclick="onMessageListDoubleClick">
        <p v-if="chatStore?.nextReqMessageID !== 'null'" class="message-more" @click="getHistoryMessageList">
          {{ TUITranslateService.t('TUIChat.查看更多') }}
        </p>
        {{ messageList?.length }}
        <li v-for="(item, index) in messageList" :id="'tui-' + item.ID" :key="item.ID" ref="messageElementListRef"
          class="message-li">
          <MessageTimestamp :currTime="item.time" :prevTime="index > 0 ? messageList[index - 1]?.time : 0" />
          <div class="message-item" v-if="showMessage(item)">
            <MessageTip v-if="item.type === TYPES.MSG_GRP_TIP || isCreateGroupCustomMessage(item)"
              :content="item.getMessageContent()" :blinkMessageIDList="blinkMessageIDList"
              @blinkMessage="blinkMessage" />
            <MessageRevoked v-else-if="item.isRevoked" :isEdit="item.type === TYPES.MSG_TEXT"
              :messageItem="shallowCopyMessage(item)" @messageEdit="handleEdit(item)" />
            <MessagePlugin v-else-if="isPluginMessage(item)" :message="deepCopy(item)"
              :blinkMessageIDList="blinkMessageIDList" @resendMessage="resendMessage"
              @handleToggleMessageItem="handleToggleMessageItem" @handleH5LongPress="handleH5LongPress" />
            <div v-else :class="{
              'message-event-bind-div': true,
            }" @longpress="handleToggleMessageItem($event, item, true)"
              @click.prevent.right="handleToggleMessageItemForPC($event, item)"
              @touchstart="handleH5LongPress($event, item, 'touchstart')"
              @touchend="handleH5LongPress($event, item, 'touchend')"
              @mouseover="handleH5LongPress($event, item, 'touchend')">
              <div class="message-overlay" :style="{
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0,
                backgroundColor: 'rgba(255,255,255,0)',
                zIndex: 999,
                pointerEvents: 'all',
              }" v-if="isMultipleSelectMode" @click.stop.prevent="
                () => {
                  if (isMessageSelectable(item)) {
                    changeSelectMessageIDList({
                      type: multipleSelectedMessageIDList.includes(item.ID) ? 'remove' : 'add',
                      messageID: item.ID,
                    })
                  }
                }
              "></div>
              <MessageBubble :content="item.getMessageContent()" :isAudioPlayed="Boolean(audioPlayedMapping[item.ID])"
                :blinkMessageIDList="blinkMessageIDList" :isMultipleSelectMode="isMultipleSelectMode"
                :messageItem="JSON.parse(JSON.stringify(item))"
                :multipleSelectedMessageIDList="multipleSelectedMessageIDList"
                :isMessageSelectable="isMessageSelectable(item)" @blinkMessage="blinkMessage"
                @resendMessage="resendMessage(item)" @changeSelectMessageIDList="changeSelectMessageIDList"
                @setReadReceiptPanelVisible="setReadReceiptPanelVisible">
                <template #messageElement>
                  <MessageText v-if="item.type === TYPES.MSG_TEXT" :content="item.getMessageContent()"
                    :messageItem="item" />
                  <ProgressMessage v-else-if="item.type === TYPES.MSG_IMAGE" :content="item.getMessageContent()"
                    :messageItem="item">
                    <MessageImage :content="item.payload.imageInfoArray[0]"
                      :imageUrl="item.payload.imageInfoArray[0].imageUrl" :messageItem="item"
                      @previewImage="handleImagePreview" />
                  </ProgressMessage>
                  <ProgressMessage v-else-if="item.type === TYPES.MSG_VIDEO" :content="item.getMessageContent()"
                    :messageItem="item">
                    <MessageVideo :content="item.getMessageContent()" :messageItem="item" />
                  </ProgressMessage>
                  <MessageAudio v-else-if="item.type === TYPES.MSG_AUDIO" :content="item.getMessageContent()"
                    :messageItem="item" @setAudioPlayed="setAudioPlayed" />
                  <ProgressMessage v-else-if="item.type === TYPES.MSG_FILE" :content="item.getMessageContent()"
                    :messageItem="item">
                    <MessageFile :content="item.getMessageContent()" :messageItem="item" />
                  </ProgressMessage>
                  <MessageRecord v-else-if="item.type === TYPES.MSG_MERGER" :renderData="item.payload"
                    :messageItem="item" />
                  <MessageFace v-else-if="item.type === TYPES.MSG_FACE" :content="item.getMessageContent()" />
                  <MessageLocation v-else-if="item.type === TYPES.MSG_LOCATION" :content="item.getMessageContent()" />
                  <MessageStreamMarkdown v-else-if="isBotMessage(item)" :payloadData="item.payload.data" :message="item"
                    @onStreaming="scrollStreamMessageToBottom" />
                  <MessageCustom v-else-if="item.type === TYPES.MSG_CUSTOM" :content="item.getMessageContent()"
                    :messageItem="item" @openLink="openLink" />
                </template>
                <template #TUIEmojiPlugin>
                  <TUIEmojiPlugin v-if="isShowEmojiPlugin && item.reactionList.length > 0" type="reaction-detail"
                    :emojiConfig="emojiConfig" :message="shallowCopyMessage(item)" />
                </template>
              </MessageBubble>
            </div>
            <!-- message tool -->
            <MessageTool v-if="item.ID === toggleID" ref="messageToolListRef" :class="{
              'message-tool': true,
              'message-tool-out': item.flow === 'out',
              'message-tool-in': item.flow === 'in',
              'message-tool-bottom': isTopMessageDom,
            }" :messageItem="item" :isMultipleSelectMode="isMultipleSelectMode"
              @toggleMultipleSelectMode="() => emits('toggleMultipleSelectMode')">
              <template #TUIEmojiPlugin>
                <TUIEmojiPlugin v-if="isShowEmojiPlugin" :message="item" :emojiConfig="emojiConfig" />
              </template>
            </MessageTool>
          </div>
        </li>
      </ul>
      <ScrollButton ref="scrollButtonInstanceRef" @scrollToLatestMessage="scrollToLatestMessage" />
      <Dialog v-if="reSendDialogShow" class="resend-dialog" :show="reSendDialogShow" :isH5="!isPC" :center="true"
        :isHeaderShow="isPC" @submit="resendMessageConfirm()" @update:show="e => (reSendDialogShow = e)">
        <p class="delDialog-title">
          {{ TUITranslateService.t('TUIChat.确认重发该消息？') }}
        </p>
      </Dialog>
      <ImagePreviewer v-if="showImagePreview" :currentImage="currentImagePreview" :imageList="imageMessageList"
        @close="onImagePreviewerClose" />
      <ReadReceiptPanel v-if="isShowReadUserStatusPanel" :message="Object.assign({}, readStatusMessage)"
        @setReadReceiptPanelVisible="setReadReceiptPanelVisible" />
    </div>

    <!-- unity 生成图片 -->
    <iframe class="unity" v-if="openUnity" :src="unityUrl"></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, computed, onMounted, onUnmounted, watch } from '../../../adapter-vue'
import TUIChatEngine, { IMessageModel, TUIStore, StoreName, TUITranslateService, TUIChatService } from '@tencentcloud/chat-uikit-engine'
import TUICore, { TUIConstants } from '@tencentcloud/tui-core'
import { outsideClick, getBoundingClientRect, getScrollInfo } from '@tencentcloud/universal-api'
import { TUIEmojiPlugin } from '@tencentcloud/tui-emoji-plugin'
// import { JoinGroupCard } from '@tencentcloud/call-uikit-vue';
import Link from './link'
import MessageGroupApplication from './message-group-application/index.vue'
import MessageText from './message-elements/message-text.vue'
import MessageImage from './message-elements/message-image.vue'
import MessageAudio from './message-elements/message-audio.vue'
import MessageRecord from './message-elements/message-record/index.vue'
import MessageFile from './message-elements/message-file.vue'
import MessageFace from './message-elements/message-face.vue'
import MessageCustom from './message-elements/message-custom.vue'
import MessageTip from './message-elements/message-tip.vue'
import MessageBubble from './message-elements/message-bubble.vue'
import MessageLocation from './message-elements/message-location.vue'
import MessageTimestamp from './message-elements/message-timestamp.vue'
import MessageVideo from './message-elements/message-video.vue'
import MessageTool from './message-tool/index.vue'
import MessageRevoked from './message-tool/message-revoked.vue'
import MessagePlugin from '../../../plugins/plugin-components/message-plugin.vue'
import MessageStreamMarkdown from './message-elements/message-stream-markdown/index.vue'
import { isBotMessage } from './message-elements/message-stream-markdown/index'
import ScrollButton from './scroll-button/index.vue'
import ReadReceiptPanel from './read-receipt-panel/index.vue'
import { isPluginMessage } from '../../../plugins/plugin-components/index'
import Dialog from '../../common/Dialog/index.vue'
import ImagePreviewer from '../../common/ImagePreviewer/index.vue'
import ProgressMessage from '../../common/ProgressMessage/index.vue'
import { emojiConfig } from '../emoji-config'
import { isPC, isH5 } from '../../../utils/env'
import chatStorage from '../utils/chatStorage'
import { throttle } from '../../../utils/lodash'
import { isEnabledMessageReadReceiptGlobal, shallowCopyMessage, isCreateGroupCustomMessage, deepCopy } from '../utils/utils'
import { sendMsg } from '@/api/aiChat'
import useChatStore from '@/store/modules/chat'
import { getSessionMessage } from '@/api/chatHistory'
import { IMCallbackHandler } from '@/utils/imCallbackHandler'

interface ScrollConfig {
  scrollToMessage?: IMessageModel
  scrollToBottom?: boolean
  scrollToOffset?: {
    top?: number
    bottom?: number
  }
}

interface IProps {
  isGroup: boolean
  groupID: string
  isNotInGroup: boolean
  isMultipleSelectMode: boolean
  userId: string
  toAccount: string
}

interface IEmits {
  (key: 'closeInputToolBar'): void
  (key: 'toggleMultipleSelectMode'): void
  (key: 'handleEditor', message: IMessageModel, type: string): void
}

const emits = defineEmits<IEmits>()
const props = withDefaults(defineProps<IProps>(), {
  isGroup: false,
  groupID: '',
  isNotInGroup: false,
  isMultipleSelectMode: false,
  userId: '',
  toAccount: '',
})

let groupType: string | undefined
let observer: IntersectionObserver | null = null
const sentReceiptMessageIDSet = new Set<string>()
const isOfficial = TUIStore.getData(StoreName.APP, 'isOfficial')
const enabledEmojiPlugin = TUIStore.getData(StoreName.APP, 'enabledEmojiPlugin')

const messageListRef = ref<HTMLElement>()
const messageToolListRef = ref<Array<{ messageToolDom: HTMLElement }>>()
// The messageList displayed on the screen, not including messages where isDeleted is true
const messageList = ref<IMessageModel[]>()
// All messageList, including messages where isDeleted is true
const multipleSelectedMessageIDList = ref<string[]>([])
const isCompleted = ref(false)
const currentConversationID = ref('')
const currentLastMessage = ref<IMessageModel>()
const nextReqMessageID = ref()
const toggleID = ref('')
const TYPES = ref(TUIChatEngine.TYPES)
const isLongpressing = ref(false)
const messageTarget = ref<IMessageModel>()
const messageElementListRef = ref<HTMLElement[] | null>()
const targetMessageDom = ref<HTMLElement | null>()
const blinkMessageIDList = ref<string[]>([])
const scrollButtonInstanceRef = ref<InstanceType<typeof ScrollButton>>()
const isShowReadUserStatusPanel = ref<boolean>(false)
const readStatusMessage = ref<IMessageModel>()
const beforeHistoryGetScrollHeight = ref<number>(0)
const isTopMessageDom = ref<boolean>(false)
const audioPlayedMapping = ref<Record<string, boolean>>({})

// image preview
const showImagePreview = ref(false)
const currentImagePreview = ref<IMessageModel>()
const imageMessageList = computed(() =>
  messageList?.value?.filter((item: IMessageModel) => {
    return !item.isRevoked && !item.hasRiskContent && item.type === TYPES.value.MSG_IMAGE
  })
)

// resend message dialog
const reSendDialogShow = ref(false)
const resendMessageData = ref()
const openUnity = ref(false)
const unityUrl = ref('')

const chatStore = useChatStore()

// 添加一个Set来保存已经滚动过的消息ID
const scrolledStatusMessageIDs = ref(new Set<string>())

// 用户交互状态管理
const isUserInteracting = ref(false) // 用户是否正在交互
const userInteractionTimer = ref<number | null>(null) // 交互超时定时器
const lastUserScrollTime = ref(0) // 最后一次用户滚动时间
const isAutoScrolling = ref(false) // 是否正在自动滚动
const INTERACTION_TIMEOUT = 3000 // 交互超时时间（3秒）

// 新消息跟踪管理 - 更精确的新消息判断
const newMessagesTracker = ref(
  new Map<
    string,
    {
      messageId: string
      initialStatus: string | null
      hasBeenScrolled: boolean
      isRealNewMessage: boolean // 是否为真正的新消息（而非状态更新）
    }
  >()
)
const hasNewMessageDuringInteraction = ref(false) // 用户交互期间是否有新消息

// 事件监听器引用，用于正确移除事件监听器
let scrollEventHandler: ((event: Event) => void) | null = null

const openLink = (url: string) => {
  unityUrl.value = url
  openUnity.value = true
}

/**
 * 设置用户交互状态
 * @param interacting 是否正在交互
 */
function setUserInteracting(interacting: boolean) {
  isUserInteracting.value = interacting

  // 清除之前的定时器
  if (userInteractionTimer.value) {
    clearTimeout(userInteractionTimer.value)
    userInteractionTimer.value = null
  }

  if (interacting) {
    // 用户开始交互，设置超时恢复
    userInteractionTimer.value = setTimeout(async () => {
      isUserInteracting.value = false
      userInteractionTimer.value = null
      console.log('用户交互超时，恢复自动滚动')

      // 恢复自动滚动后，检查是否需要滚动到底部
      await checkAndScrollToBottomOnRestore()
    }, INTERACTION_TIMEOUT)
  }
}

/**
 * 判断是否为新消息或状态变化（都应触发滚动行为）
 * @param message 消息对象
 * @param isNewToList 是否为列表中的新消息
 */
function isNewMessageOrStatusChange(message: IMessageModel, isNewToList: boolean): boolean {
  const messageId = message.ID
  const tracker = newMessagesTracker.value.get(messageId)

  // 获取当前消息状态
  let currentStatus: string | null = null
  try {
    const data = JSON.parse(message.payload?.data || '{}')
    currentStatus = data?.status || null
  } catch (e) {
    // 解析失败，可能是普通消息，视为状态变化
    currentStatus = null
  }

  if (!tracker) {
    // 第一次见到这个消息ID，判断为新消息
    newMessagesTracker.value.set(messageId, {
      messageId,
      initialStatus: currentStatus,
      hasBeenScrolled: false,
      isRealNewMessage: isNewToList,
    })

    return isNewToList
  } else {
    // 已存在的消息，检查状态是否发生变化
    const hasStatusChanged = tracker.initialStatus !== currentStatus

    if (hasStatusChanged) {
      // 状态发生变化，更新跟踪器并视为新消息
      tracker.initialStatus = currentStatus
      tracker.hasBeenScrolled = false // 重置滚动标记，允许重新滚动
      console.log(`消息 ${messageId} 状态变化: ${tracker.initialStatus} → ${currentStatus}`)
      return true
    }

    // 状态未变化，不视为新消息
    return false
  }
}

/**
 * 交互恢复后检查并滚动到底部
 * 如果用户交互期间有新消息或消息状态变化且当前不在底部位置，则滚动到底部
 */
async function checkAndScrollToBottomOnRestore() {
  // 只有在用户交互期间有新消息或状态变化且当前不在底部时才滚动
  if (hasNewMessageDuringInteraction.value && !isCurrentListInBottomPosition()) {
    console.log('用户交互期间有新消息或状态变化且不在底部，自动滚动到最新消息')
    const scrolled = await smartScrollToBottom()

    // 如果成功滚动且滚动按钮可见，重置新消息计数
    if (scrolled && scrollButtonInstanceRef.value?.isScrollButtonVisible) {
      scrollButtonInstanceRef.value.resetNewMessageCount?.()
    }
  } else {
    console.log('交互恢复后无需滚动')
  }

  // 重置新消息标记和清理过期的跟踪记录
  hasNewMessageDuringInteraction.value = false
  cleanupOldTrackers()
}

/**
 * 清理过期的消息跟踪记录
 */
function cleanupOldTrackers() {
  const currentMessageIds = new Set(messageList.value?.map(msg => msg.ID) || [])

  // 删除不在当前消息列表中的跟踪记录
  for (const [messageId] of newMessagesTracker.value) {
    if (!currentMessageIds.has(messageId)) {
      newMessagesTracker.value.delete(messageId)
    }
  }
}

/**
 * 检测是否为用户主动滚动
 * @param event 滚动事件
 */
function isUserScroll(event?: Event): boolean {
  const now = Date.now()

  // 如果正在自动滚动，则不是用户滚动
  if (isAutoScrolling.value) {
    return false
  }

  // 如果距离上次用户滚动时间很短，认为是用户连续滚动
  if (now - lastUserScrollTime.value < 100) {
    lastUserScrollTime.value = now
    return true
  }

  // 更新最后滚动时间
  lastUserScrollTime.value = now
  return true
}

/**
 * 处理用户滚动事件
 */
function handleUserScroll(event: Event) {
  if (isUserScroll(event)) {
    setUserInteracting(true)
    console.log('检测到用户滚动，暂停自动滚动')
  }
}

/**
 * 处理用户鼠标/触摸交互
 */
function handleUserInteraction() {
  setUserInteracting(true)
  console.log('检测到用户交互，暂停自动滚动')
}

/**
 * 智能滚动到底部
 * 只有在用户未交互时才执行自动滚动
 */
async function smartScrollToBottom(): Promise<boolean> {
  // 如果用户正在交互，则不自动滚动
  if (isUserInteracting.value) {
    console.log('用户正在交互，跳过自动滚动')
    return false
  }

  // 标记正在自动滚动
  isAutoScrolling.value = true

  try {
    await scrollToPosition({ scrollToBottom: true })
    console.log('自动滚动到底部完成')
    return true
  } finally {
    // 延迟重置自动滚动标记，避免滚动事件误判
    setTimeout(() => {
      isAutoScrolling.value = false
    }, 100)
  }
}
const showMessage = computed(() => {
  return (item: IMessageModel) => {
    // 检查是否为tips消息
    if (item?.payload?.data) {
      try {
        const data = JSON.parse(item.payload.data)
        const isTipsMessage = data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0 && !data?.text?.length

        if (!isTipsMessage) {
          return true
        }

        // 如果是tips消息，检查是否是最后一条tips消息
        const lastTipsID = getLastTipsMessageID()
        console.log('🚀 ~ return ~ lastTipsID:', lastTipsID)
        console.log('🚀 ~ return ~ item:', item)
        return item.ID === lastTipsID
      } catch (error) {
        console.error('解析消息数据失败:', error)
        return true
      }
    }
    return true
  }
})
// 直接调用 getLastTipsMessageID 方法的逻辑
const getLastTipsMessageID = () => {
  // 使用已合并历史消息的messageList.value而不是TUIStore中的原始数据
  const currentMessageList = messageList.value || []
  if (!currentMessageList.length) return ''

  let lastTipsID = ''
  let foundTips = false

  // 从后向前遍历消息列表
  for (let i = currentMessageList.length - 1; i >= 0; i--) {
    const message = currentMessageList[i]

    // 跳过已删除的消息
    if (message.isDeleted) continue

    // 检查消息的payload.data
    if (message.payload?.data) {
      try {
        const data = JSON.parse(message.payload.data)

        if (!foundTips) {
          // 检查当前消息是否为允许的自定义消息类型
          const isAllowedCustomMessage =
            data.businessID === 'ai_event' || data.businessID === 'ai_stop' || data.businessID === 'ai_signal' || (data.businessID === 'ai_event' && data.content?.name === 'delete')

          if (!isAllowedCustomMessage) {
            // 检查是否为tips消息
            if (data.businessID === 'ai_message' && Array.isArray(data?.tips) && data.tips.length > 0) {
              lastTipsID = message.ID
              foundTips = true
            }
            // 如果既不是允许的自定义消息也不是tips，直接返回空
            else {
              return ''
            }
          }
        } else {
          // 已找到tips消息，确保之后的消息都是允许的类型
          return lastTipsID
        }
      } catch (error) {
        console.error('解析消息数据失败:', error, message.payload.data)
        continue
      }
    }
  }

  return lastTipsID
}
const isShowEmojiPlugin = computed(() => {
  const msgPopMenuExtensionList = TUICore.getExtensionList(TUIConstants.TUIChat.EXTENSION.MSG_POP_MENU.EXT_ID, {
    enabledEmojiPlugin,
  })
  return msgPopMenuExtensionList.some(item => {
    return item.text === 'TUIEmojiPlugin'
  })
})

const iframeMessage = (e: any) => {
  try {
    let data = JSON.parse(e.data)
    console.log('data', data)
    sendMsg(
      {
        type: 'THREE-SHOW',
        content: {
          three: data.url1,
          four: data.url2,
        },
      },
      {
        fromAccount: props.userId,
        groupId: props.toAccount,
      }
    ).then(res => {
      console.log('res', res)
      if (res.data.code === 200) {
        unityUrl.value = ''
        openUnity.value = false
      }
    })
  } catch (error) {
    // console.log(error)
  }
}

onMounted(() => {
  console.log('init iframeMessage')
  window.addEventListener('message', iframeMessage)

  // Retrieve the information about whether the audio has been played from localStorage
  audioPlayedMapping.value = chatStorage.getChatStorage('audioPlayedMapping') || {}

  TUIStore.watch(StoreName.CHAT, {
    messageList: onMessageListUpdated,
    messageSource: onMessageSourceUpdated,
    isCompleted: isCompletedUpdated,
  })

  TUIStore.watch(StoreName.CONV, {
    currentConversationID: onCurrentConversationIDUpdated,

  })

  TUIStore.watch(StoreName.CUSTOM, {
    isShowMessagePopMenu: isShowMessagePopMenuUpdated,
  })

  // 创建滚动事件处理器
  scrollEventHandler = (event: Event) => {
    // 原有的滚动处理逻辑
    handelScrollListScroll(event)
    // 新增的用户交互检测
    handleUserScroll(event)
  }

  // 添加滚动事件监听器
  messageListRef.value?.addEventListener('scroll', scrollEventHandler)

  // 添加用户交互事件监听器
  const messageListElement = messageListRef.value
  if (messageListElement) {
    // 鼠标事件
    messageListElement.addEventListener('mousedown', handleUserInteraction)
    messageListElement.addEventListener('wheel', handleUserInteraction)

    // 触摸事件（移动端）
    messageListElement.addEventListener('touchstart', handleUserInteraction)
    messageListElement.addEventListener('touchmove', handleUserInteraction)

    // 键盘事件
    messageListElement.addEventListener('keydown', handleUserInteraction)
  }

  // 初始加载时确保滚动到底部
  nextTick(() => {
    scrollToPosition({ scrollToBottom: true })
  })
})

onUnmounted(() => {
  window.removeEventListener('message', iframeMessage)

  TUIStore.unwatch(StoreName.CHAT, {
    messageList: onMessageListUpdated,
    messageSource: onMessageSourceUpdated,
    isCompleted: isCompletedUpdated,
  })

  TUIStore.unwatch(StoreName.CONV, {
    currentConversationID: onCurrentConversationIDUpdated,
  })

  TUIStore.unwatch(StoreName.CUSTOM, {
    isShowMessagePopMenu: isShowMessagePopMenuUpdated,
  })

  // 移除滚动事件监听器
  if (messageListRef.value && scrollEventHandler) {
    messageListRef.value.removeEventListener('scroll', scrollEventHandler)
    scrollEventHandler = null

    // 移除用户交互事件监听器
    messageListRef.value.removeEventListener('mousedown', handleUserInteraction)
    messageListRef.value.removeEventListener('wheel', handleUserInteraction)
    messageListRef.value.removeEventListener('touchstart', handleUserInteraction)
    messageListRef.value.removeEventListener('touchmove', handleUserInteraction)
    messageListRef.value.removeEventListener('keydown', handleUserInteraction)
  }

  // 清理交互状态
  if (userInteractionTimer.value) {
    clearTimeout(userInteractionTimer.value)
    userInteractionTimer.value = null
  }
  isUserInteracting.value = false
  isAutoScrolling.value = false
  hasNewMessageDuringInteraction.value = false

  // 清理消息跟踪器
  newMessagesTracker.value.clear()
  scrolledStatusMessageIDs.value.clear()

  if (Object.keys(audioPlayedMapping.value).length > 0) {
    // Synchronize storage about whether the audio has been played when the component is unmounted
    chatStorage.setChatStorage('audioPlayedMapping', audioPlayedMapping.value)
  }

  sentReceiptMessageIDSet.clear()
  observer?.disconnect()
  observer = null
})

async function onMessageListUpdated(list: IMessageModel[]) {
  observer?.disconnect()
  const oldLastMessage = currentLastMessage.value
  let hasEmojiReaction = false

  // 过滤当前消息列表
  const filteredCurrentMessages = list.filter((message, index) => {
    // 过滤掉已删除的消息
    if (message.isDeleted) {
      return false
    }

    if (!message?.payload?.data) {
      return true
    }

    const data = JSON.parse(message.payload.data)
    // // 如果当前消息是AI消息，并且有tips，并且当前消息不是最后一条消息，则不显示
    // if (data?.businessID === 'ai_message' && Array.isArray(data?.tips) && data?.tips.length > 0 && list[index + 1] && !data?.text?.length) {
    //   return false
    // }
    return data.businessID !== 'hidden_message' && data.businessID !== 'ai_signal' && data.businessID !== 'ai_say' && data.businessID !== 'ai_event' && data.businessID !== 'say_hidden_message'
  })

  // // 将chatStore.historyMessages拼接到过滤后的消息列表前面
  // messageList.value = [...(chatStore.historyMessages || []), ...filteredCurrentMessages]
  messageList.value = filteredCurrentMessages
  // console.log("🚀 ~ onMessageListUpdated ~  messageList.value:", messageList.value)
  if (!messageList.value?.length) {
    currentLastMessage.value = undefined
    return
  }
  const newLastMessage = messageList.value?.[messageList.value?.length - 1]

  // 优先处理特殊滚动场景（这些场景不受用户交互状态影响）
  if (messageTarget.value) {
    // 滚动到指定消息（如点击消息引用）
    if (messageList.value?.findIndex((message: IMessageModel) => message?.ID === messageTarget.value?.ID) >= 0) {
      const tempMessage = messageTarget.value
      messageTarget.value = undefined
      await scrollToPosition({ scrollToMessage: tempMessage })
      await blinkMessage(tempMessage?.ID)
    }
  } else if (beforeHistoryGetScrollHeight.value) {
    // 获取历史消息后保持滚动位置
    await scrollToPosition({
      scrollToOffset: { bottom: beforeHistoryGetScrollHeight.value },
    })
    beforeHistoryGetScrollHeight.value = 0
  } else if (newLastMessage?.ID && JSON.stringify(oldLastMessage) !== JSON.stringify(newLastMessage)) {
    // 有新消息或状态变化时的智能滚动逻辑
    let shouldScroll = true

    // 判断是否为新消息或状态变化
    const isNewMessage = !oldLastMessage?.ID || oldLastMessage.ID !== newLastMessage.ID
    const isNewOrStatusChanged = isNewMessageOrStatusChange(newLastMessage, isNewMessage)

    // 检查消息状态，避免在同一状态下重复滚动
    if (newLastMessage.payload?.data) {
      try {
        const data = JSON.parse(newLastMessage.payload.data)
        console.log("🚀 ~ onMessageListUpdated ~ data:", data?.status)
        const messageId = newLastMessage.ID
        const tracker = newMessagesTracker.value.get(messageId)

        // 仅在消息状态为 'success' 或 'fail' 时才跳过滚动
        if (data?.status === 'SUCCESS' || data?.status === 'FAIl') {
          // 如果跟踪器显示已经为当前状态滚动过，则不再滚动
          if (tracker?.hasBeenScrolled && tracker.initialStatus === data?.status) {
            shouldScroll = false
            console.log(`消息 ${messageId} 在状态 ${data?.status} 下已滚动过，跳过`)
          } else {
            // 记录已滚动的消息ID和状态
            scrolledStatusMessageIDs.value.add(messageId)
            if (tracker) {
              tracker.hasBeenScrolled = true
            }
            console.log(`消息 ${messageId} 在状态 ${data?.status} 下首次滚动`)
          }
        } else {
          // 对于其他状态，总是允许滚动
          if (tracker) {
            tracker.hasBeenScrolled = true
          }
        }
      } catch (e) {
        console.error('解析消息数据失败:', e)
      }
    }

    if (shouldScroll) {
      // 使用智能滚动，只有在用户未交互时才自动滚动
      const scrolled = await smartScrollToBottom()

      if (scrolled) {
        // 如果成功滚动且滚动按钮可见，重置新消息计数
        if (scrollButtonInstanceRef.value?.isScrollButtonVisible) {
          scrollButtonInstanceRef.value.resetNewMessageCount?.()
        }
      } else {
        // 如果用户正在交互，无法自动滚动，新消息或状态变化都标记
        if (isNewOrStatusChanged) {
          hasNewMessageDuringInteraction.value = true
          if (isNewMessage) {
            console.log('用户交互期间有新消息到达')
          } else {
            console.log('用户交互期间有消息状态变化')
          }
        }
      }
    }
  } else if (hasEmojiReaction && isCurrentListInBottomPosition()) {
    // 表情反应场景：只有在底部时才滚动
    await smartScrollToBottom()
  }

  currentLastMessage.value = Object.assign({}, newLastMessage)
  if (isEnabledMessageReadReceiptGlobal()) {
    nextTick(() => bindIntersectionObserver())
  }
}

function isCurrentListInBottomPosition() {
  return (
    messageListRef.value &&
    typeof messageListRef.value.scrollTop === 'number' &&
    typeof messageListRef.value.scrollHeight === 'number' &&
    typeof messageListRef.value.clientHeight === 'number' &&
    Math.ceil(messageListRef.value.scrollTop + messageListRef.value.clientHeight) >= messageListRef.value.scrollHeight
  )
}

async function scrollStreamMessageToBottom() {
  // 使用nextTick确保在DOM更新后滚动
  await nextTick()

  // 检查当前最后一条消息的状态
  const lastMessage = messageList.value?.[messageList.value?.length - 1]
  if (lastMessage?.payload?.data) {
    try {
      const data = JSON.parse(lastMessage.payload.data)
      const messageId = lastMessage.ID
      const tracker = newMessagesTracker.value.get(messageId)

      // 检查是否为状态变化，如果是则允许滚动
      const isStatusChange = tracker && tracker.initialStatus !== data?.status

      // 仅在消息状态为 'success' 或 'fail' 时才跳过滚动
      if (data?.status === 'success' || data?.status === 'fail') {
        if (!isStatusChange && tracker?.hasBeenScrolled && tracker.initialStatus === data?.status) {
          console.log(`流式消息 ${messageId} 在状态 ${data?.status} 下已滚动过，跳过`)
          return
        }
      }

      // 记录已滚动的消息ID和更新状态
      if (data?.status === 'success' || data?.status === 'fail') {
        scrolledStatusMessageIDs.value.add(messageId)
        if (tracker) {
          tracker.hasBeenScrolled = true
          tracker.initialStatus = data?.status // 更新状态
        }
        console.log(`流式消息 ${messageId} 在状态 ${data?.status} 下滚动`)
      }
    } catch (e) {
      console.error('解析消息数据失败:', e)
    }
  }

  // 流式消息滚动使用智能滚动
  const scrolled = await smartScrollToBottom()

  // 如果成功滚动且滚动按钮可见，重置新消息计数
  if (scrolled && scrollButtonInstanceRef.value?.isScrollButtonVisible) {
    scrollButtonInstanceRef.value.resetNewMessageCount?.()
  }
}

async function scrollToPosition(config: ScrollConfig = {}): Promise<void> {
  return new Promise((resolve, reject) => {
    requestAnimationFrame(() => {
      if (!messageListRef.value) {
        reject()
      }
      const container = messageListRef.value
      if (config.scrollToBottom) {
        container!.scrollTop = container!.scrollHeight
      } else if (config.scrollToMessage) {
        const targetMessageDom = messageElementListRef.value?.find((dom: HTMLElement) => dom?.id === `tui-${config.scrollToMessage?.ID}`)
        if (targetMessageDom?.scrollIntoView) {
          targetMessageDom.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
          })
        }
      } else if (config.scrollToOffset) {
        if (config.scrollToOffset?.top) {
          container!.scrollTop = config.scrollToOffset.top
        } else if (config.scrollToOffset?.bottom) {
          container!.scrollTop = container!.scrollHeight - config.scrollToOffset.bottom
        }
      }
      resolve()
    })
  })
}

async function onMessageSourceUpdated(message: IMessageModel) {
  // messageSource change has two cases
  // 1. messageSource change -> cache miss -> messageList change,
  // 2. messageSource change -> cache hit -> messageList not change
  // Only the second case needs to add scrollToTarget when listening here
  messageTarget.value = message
  if (messageTarget.value) {
    if (messageList.value?.findIndex((message: IMessageModel) => message?.ID === messageTarget.value?.ID) >= 0) {
      const tempMessage = messageTarget.value
      messageTarget.value = undefined
      await scrollToPosition({ scrollToMessage: tempMessage })
      await blinkMessage(tempMessage?.ID)
    }
  }
}

function isCompletedUpdated(flag: boolean) {
  console.log("🚀 ~ isCompletedUpdated ~ flag:", flag)
  isCompleted.value = flag
}

function isShowMessagePopMenuUpdated(isShow: boolean) {
  if (!isShow) {
    toggleID.value = ''
  }
}

const onCurrentConversationIDUpdated = (conversationID: string) => {
  currentConversationID.value = conversationID
  if (!currentConversationID.value) {
    messageList.value = []
  }

  if (isEnabledMessageReadReceiptGlobal()) {
    const { groupProfile } = TUIStore.getConversationModel(conversationID) || {}
    groupType = groupProfile?.type
  }
  if (Object.keys(audioPlayedMapping.value).length > 0) {
    // Synchronize storage about whether the audio has been played when converstaion switched
    chatStorage.setChatStorage('audioPlayedMapping', audioPlayedMapping.value)
  }
  // 切换会话时重置已滚动消息ID记录
  scrolledStatusMessageIDs.value.clear()

  // 切换会话时重置用户交互状态，允许新会话自动滚动
  setUserInteracting(false)
  // 重置新消息标记
  hasNewMessageDuringInteraction.value = false
  console.log('会话切换，重置用户交互状态')
}

const getHistoryMessageList = () => {
  // TUIChatService.getMessageList().then((res: any) => {
  //   console.log('====>', res)
  //   const { nextReqMessageID: ID } = res.data
  //   nextReqMessageID.value = ID
  // })
  // // After getting the historical messages, keep the scroll bar in the original position
  // beforeHistoryGetScrollHeight.value = messageListRef.value?.scrollHeight || 0
  getSessionMessage({
    sessionId: chatStore?.sessionId,
    minId: chatStore?.nextReqMessageID,
  }).then((res: any) => {
    console.log('🚀 ~ handleSelectHistoryItem ~ res:', res.data)
    // 在这里调用IMCallbackHandler.processIMCallbacks
    const messages = IMCallbackHandler.processIMCallbacks(res.data)
    console.log("🚀 ~ handleSelectHistoryItem ~ messages:", messages)
    if (res?.data?.length > 0) {
      chatStore?.setNextReqMessageID(res?.data[res?.data?.length - 1]?.id)
    } else {
      chatStore?.setNextReqMessageID('null')
    }
    // console.log('🚀 ~ handleSelectHistoryItem ~ messages:', messages)
    // 把这个消息存到store里
    chatStore.setHistoryMessages(messages)

  })
}

const openComplaintLink = (type: any) => {
  window.open(type.url)
}

const handleImagePreview = (message: IMessageModel) => {
  if (showImagePreview.value || currentImagePreview.value || isLongpressing.value) {
    return
  }
  showImagePreview.value = true
  currentImagePreview.value = message
}

const onImagePreviewerClose = () => {
  showImagePreview.value = false
  currentImagePreview.value = undefined
}

// toggle message
const handleToggleMessageItem = (e: any, message: IMessageModel, isLongpress = false) => {
  if (props.isMultipleSelectMode || props.isNotInGroup) {
    return
  }
  if (isLongpress) {
    isLongpressing.value = true
  }
  toggleID.value = message.ID
  filterTopMessageDom(e.target)
}

const handleToggleMessageItemForPC = (e: MouseEvent, message: IMessageModel) => {
  if (props.isMultipleSelectMode || props.isNotInGroup) {
    return
  }
  if (isPC) {
    toggleID.value = message.ID
    targetMessageDom.value = messageElementListRef.value?.find((dom: HTMLElement) => dom?.id === `tui-${message.ID}`)
    nextTick(() => {
      const ignoreDomRefs = messageToolListRef.value && messageToolListRef.value[0]?.messageToolDom
      outsideClick.listen({
        domRefs: targetMessageDom.value,
        ignoreDomRefs: ignoreDomRefs,
        handler: closeChatPop,
        button: e.button,
      })
      filterTopMessageDom(e.target)
    })
  }
}

function filterTopMessageDom(toggleMessageElement: any) {
  const chatElement = document.getElementById('tui-chat-main')
  const safeTop = 160
  const messageElementRect = toggleMessageElement.getBoundingClientRect()
  const ChatElementRect = chatElement?.getBoundingClientRect()
  if (ChatElementRect) {
    isTopMessageDom.value = messageElementRect.top - ChatElementRect.top < safeTop ? true : false
  }
}

// h5 long press
let timer: number
const handleH5LongPress = (e: any, message: IMessageModel, type: string) => {
  if (props.isMultipleSelectMode || props.isNotInGroup) {
    return
  }
  if (!isH5) return

  // 检查事件是否来自消息气泡区域
  const target = e.target as HTMLElement
  const messageBubble = target.closest('.message-body')

  if (!messageBubble) {
    return
  }

  function longPressHandler() {
    clearTimeout(timer)
    handleToggleMessageItem(e, message)
  }
  function touchStartHandler() {
    timer = setTimeout(longPressHandler, 500)
  }
  function touchEndHandler() {
    clearTimeout(timer)
  }
  switch (type) {
    case 'touchstart':
      touchStartHandler()
      break
    case 'touchend':
      touchEndHandler()
      setTimeout(() => {
        isLongpressing.value = false
      }, 200)
      break
  }
}

// re-edit message
const handleEdit = (message: IMessageModel) => {
  emits('handleEditor', message, 'reedit')
}

const resendMessage = (message: IMessageModel) => {
  reSendDialogShow.value = true
  resendMessageData.value = message
}

const resendMessageConfirm = () => {
  reSendDialogShow.value = !reSendDialogShow.value
  const messageModel = resendMessageData.value
  messageModel.resendMessage()
}

function blinkMessage(messageID: string): Promise<void> {
  return new Promise(resolve => {
    const index = blinkMessageIDList.value.indexOf(messageID)
    if (index < 0) {
      blinkMessageIDList.value.push(messageID)
      const timer = setTimeout(() => {
        blinkMessageIDList.value.splice(blinkMessageIDList.value.indexOf(messageID), 1)
        clearTimeout(timer)
        resolve()
      }, 3000)
    }
  })
}

async function scrollToLatestMessage() {
  // 用户主动点击滚动到最新消息，重置交互状态
  setUserInteracting(false)

  const { scrollHeight } = await getScrollInfo('#messageScrollList')
  const { height } = await getBoundingClientRect('#messageScrollList')
  if (messageListRef.value) {
    // 标记为自动滚动
    isAutoScrolling.value = true
    messageListRef.value.scrollTop = scrollHeight - height

    // 延迟重置自动滚动标记
    setTimeout(() => {
      isAutoScrolling.value = false
    }, 100)
  }
}

const handelScrollListScroll = throttle(
  function (e: Event) {
    scrollButtonInstanceRef.value?.judgeScrollOverOneScreen(e)
  },
  150,
  { leading: true }
)

async function bindIntersectionObserver() {
  if (!messageList.value || !messageListRef.value || messageList.value.length === 0) {
    return
  }

  if (groupType === TYPES.value.GRP_AVCHATROOM || groupType === TYPES.value.GRP_COMMUNITY) {
    // AVCHATROOM and COMMUNITY chats do not monitor read receipts for messages.
    return
  }

  const mappingFromIDToMessage: Record<
    string,
    {
      msgDom: HTMLElement
      msgModel: IMessageModel | undefined
    }
  > = {}

  observer?.disconnect()
  observer = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        const { isIntersecting, target } = entry
        if (isIntersecting) {
          const { msgDom, msgModel } = mappingFromIDToMessage[target.id]
          if (msgModel && !msgModel.readReceiptInfo?.isPeerRead && !sentReceiptMessageIDSet.has(msgModel.ID)) {
            TUIChatService.sendMessageReadReceipt([msgModel])
            sentReceiptMessageIDSet.add(msgModel.ID)
            observer?.unobserve(msgDom)
          }
        }
      })
    },
    {
      root: messageListRef.value,
      threshold: 0.7,
    }
  )

  const arrayOfMessageLi = messageListRef.value?.querySelectorAll('.message-li')
  if (arrayOfMessageLi) {
    for (let i = 0; i < arrayOfMessageLi?.length; ++i) {
      const messageElement = arrayOfMessageLi[i] as HTMLElement
      const matchingMessage = messageList.value.find((message: IMessageModel) => {
        return messageElement.id.slice(4) === message.ID
      })
      if (matchingMessage && matchingMessage.needReadReceipt && matchingMessage.flow === 'in') {
        mappingFromIDToMessage[messageElement.id] = {
          msgDom: messageElement,
          msgModel: matchingMessage,
        }
        observer?.observe(messageElement)
      }
    }
  }
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const isSignalingMessage = (message: IMessageModel) => {
  return message?.type === TYPES.value.MSG_CUSTOM && message?.getSignalingInfo()
}

function setReadReceiptPanelVisible(visible: boolean, message?: IMessageModel) {
  if (visible && props.isNotInGroup) {
    return
  }
  if (!visible) {
    readStatusMessage.value = undefined
  } else {
    readStatusMessage.value = message
  }
  isShowReadUserStatusPanel.value = visible
}

function closeChatPop() {
  toggleID.value = ''
}

function onMessageListBackgroundClick() {
  emits('closeInputToolBar')

  // 点击消息列表背景时，如果用户正在交互状态，可以选择恢复自动滚动
  // 这里我们保持原有行为，不自动恢复，让用户通过超时机制自然恢复
}

/**
 * 双击消息列表背景，立即恢复自动滚动并滚动到底部
 */
async function onMessageListDoubleClick() {
  console.log('双击消息列表，立即恢复自动滚动并滚动到底部')

  // 立即重置用户交互状态
  setUserInteracting(false)

  // 滚动到底部
  const scrolled = await smartScrollToBottom()

  // 如果成功滚动且滚动按钮可见，重置新消息计数
  if (scrolled && scrollButtonInstanceRef.value?.isScrollButtonVisible) {
    scrollButtonInstanceRef.value.resetNewMessageCount?.()
  }
}

watch(
  () => props.isMultipleSelectMode,
  newValue => {
    console.log('🚀 ~ newValue:', newValue)
    if (!newValue) {
      changeSelectMessageIDList({
        type: 'clearAll',
        messageID: '',
      })
    }
  }
)
watch(() => chatStore.historyMessages,
  newValue => {
    console.log('🚀 ~ chatStore.historyMessages:', newValue)
    messageList.value = newValue
    console.log("🚀 ~   messageList.value:", messageList.value)
  }
)

function changeSelectMessageIDList({ type, messageID }: { type: 'add' | 'remove' | 'clearAll'; messageID: string }) {
  const message: any = messageList.value?.find(msg => msg.ID === messageID)
  console.log('🚀 ~ changeSelectMessageIDList ~ message:', message)
  // if (!message) return

  if (type === 'clearAll') {
    chatStore.clearSelectedMessages()
    multipleSelectedMessageIDList.value = []
  } else if (type === 'add') {
    if (!multipleSelectedMessageIDList.value.includes(messageID)) {
      chatStore.addSelectedMessage(message)
      multipleSelectedMessageIDList.value.push(messageID)
    }
  } else if (type === 'remove') {
    chatStore.removeSelectedMessage(messageID)
    multipleSelectedMessageIDList.value = multipleSelectedMessageIDList.value.filter(id => id !== messageID)
  }
  console.log('🚀 ~ changeSelectMessageIDList ~  multipleSelectedMessageIDList.value:', multipleSelectedMessageIDList.value)
}

function mergeForwardMessage() {
  TUIStore.update(StoreName.CUSTOM, 'multipleForwardMessageID', {
    isMergeForward: true,
    messageIDList: multipleSelectedMessageIDList.value,
  })
}

function oneByOneForwardMessage() {
  TUIStore.update(StoreName.CUSTOM, 'multipleForwardMessageID', {
    isMergeForward: false,
    messageIDList: multipleSelectedMessageIDList.value,
  })
}

function setAudioPlayed(messageID: string) {
  audioPlayedMapping.value = {
    ...audioPlayedMapping.value,
    [messageID]: true,
  }
}

const isMessageSelectable = (message: IMessageModel) => {
  // 首先检查消息是否存在
  if (!message) {
    return false
  }

  // 检查是否是agent发送的消息
  const isAgentMessage = message.from?.includes('agent')

  if (isAgentMessage) {
    try {
      const payload = JSON.parse(message.payload?.data || '{}')
      if (payload?.status !== 'SUCCESS') {
        return false
      }

      // 检查是否是tips消息
      if (payload.businessID === 'ai_message' && Array.isArray(payload?.tips) && payload.tips.length > 0) {
        return false
      }

      // 检查是否有联网搜索结果
      if (payload?.searchRespList?.length > 0) {
        return false
      }

      // 检查是否是视频消息
      if (payload?.video) {
        // 使用本地的isShowVideo逻辑
        return payload?.video?.url
      }
      if (payload?.images?.length > 1) {
        return false
      }

      // 其他类型的agent消息允许选择
      return true
    } catch (error) {
      console.error('解析消息数据失败:', error)
      return false
    }
  } else {
    // 用户发送的消息直接返回true
    if (message.type === TYPES.value.MSG_IMAGE) {
      return message?.payload?.imageInfoArray[0]?.imageUrl ? true : false
    } else if (message.type === TYPES.value.MSG_VIDEO) {
      return message?.payload?.video?.url ? true : false
    }
    return true
  }
}

defineExpose({
  oneByOneForwardMessage,
  mergeForwardMessage,
  scrollToLatestMessage,
})
</script>

<style lang="scss" scoped src="./style/index.scss"></style>
<style>
.row-reverse {
  flex-direction: row-reverse;
}

.unity {
  position: fixed;
  z-index: 999;
  background: white;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  border: none;
}
</style>
